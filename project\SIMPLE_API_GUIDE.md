# PolicySelection Component - Simple API Integration

## Overview
The PolicySelection component now uses only **TWO simple API endpoints**. All dummy data has been completely removed.

## How It Works

### 1. Search Process
- User enters any combination of: Customer ID, Policy Number, Customer Name
- User clicks "Search" button
- Frontend sends request to `/api/search` endpoint
- Backend returns customer data with available policies
- Policies are displayed in a table

### 2. View Process  
- User clicks "View Details" button on any policy
- Frontend sends request to `/api/view` endpoint
- Backend returns complete customer information
- Complete policy details are displayed

## Required API Endpoints

### Endpoint 1: Search
**URL:** `POST /api/search`

**Request:**
```json
{
  "customerId": "CUS-123456",
  "policyNumber": "POL-789012", 
  "customerName": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "DOB": "05.02.1994",
    "Email": "<EMAIL>",
    "Phone": "(*************",
    "Address": "123 Main Street, New York, NY 10001",
    "Occupation": "Software Engineer",
    "Annual Income": "$85,000",
    "Customer ID": "CUS-123456",
    "Policy Number": "POL-789012",
    "Policy Type": "Whole Life Insurance",
    "Status": "Active",
    "Available Policies": [
      {
        "id": "POL-789012",
        "name": "Whole Life Insurance",
        "description": "Permanent life insurance with cash value",
        "coverage": "500,000 $",
        "premium": "2000 $ annually",
        "features": ["Cash Value Growth", "Tax Benefits", "Loan Options"]
      },
      {
        "id": "POL-789013", 
        "name": "Term Life Insurance",
        "description": "Affordable temporary coverage",
        "coverage": "750,000 $",
        "premium": "800 $ annually",
        "features": ["Lower Premiums", "Convertible Option"]
      }
    ]
  }
}
```

### Endpoint 2: View
**URL:** `POST /api/view`

**Request:**
```json
{
  "policyId": "POL-789012",
  "customerId": "CUS-123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "POL-789012",
      "name": "Whole Life Insurance",
      "description": "Permanent life insurance with cash value",
      "coverage": "500,000 $",
      "premium": "2000 $ annually",
      "features": ["Cash Value Growth", "Tax Benefits"],
      "status": "Active",
      "issueDate": "01/01/2024",
      "nextDueDate": "01/01/2025",
      "cashValue": "$25,000",
      "paymentFrequency": "Annually"
    },
    "paymentHistory": [
      {
        "date": "01/15/2024",
        "amount": "$2000",
        "status": "Paid"
      }
    ],
    "transactionHistory": [
      {
        "id": "TNX001",
        "date": "03-01-2024",
        "type": "Premium Payment",
        "amount": "2000.00",
        "remarks": "Annual premium payment"
      }
    ],
    "riders": [
      {
        "name": "Critical Illness Rider",
        "coverage": "$50,000",
        "status": "Active"
      }
    ]
  }
}
```

## Environment Setup
Set this in your `.env` file:
```
REACT_APP_API_BASE_URL=http://localhost:3001/api
```

## That's It!
Just implement these two endpoints and the component will work perfectly. No complex routing, no multiple endpoints - just search and view!
