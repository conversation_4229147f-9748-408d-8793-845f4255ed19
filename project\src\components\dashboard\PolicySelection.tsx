import React, { useState } from 'react';
import { ArrowRight, Calculator, Search, User, Shield, DollarSign, FileText, CreditCard, Activity, Loader2 } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

/**
 * PolicySelection Component - Backend Integration Ready
 *
 * This component uses only TWO API endpoints:
 *
 * 1. POST /api/search - Search for customer policies (any field can be used)
 * 2. POST /api/view - Get complete customer information for selected policy
 *
 * Environment Variables:
 * - REACT_APP_API_BASE_URL: Base URL for the backend API (default: http://localhost:3001/api)
 */

// TypeScript interfaces
interface Policy {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
  status?: string;
  issueDate?: string;
  nextDueDate?: string;
  cashValue?: string;
  paymentFrequency?: string;
}

interface CustomerDetails {
  DOB: string;
  Email: string;
  Phone: string;
  Address: string;
  Occupation: string;
  "Annual Income": string;
  "Customer ID": string;
  "Policy Number": string;
  "Policy Type": string;
  Status: string;
  "Available Policies": Policy[];
}

interface CurrentCustomerData {
  name: string;
  policyNumber: string;
  customerId: string;
  details: CustomerDetails;
}

interface PaymentHistory {
  date: string;
  amount: string;
  status: string;
}

interface TransactionHistory {
  id: string;
  date: string;
  type: string;
  amount: string;
  remarks: string;
}

interface Rider {
  name: string;
  coverage: string;
  status: string;
}

interface PolicyDetailsResponse {
  policy: Policy;
  paymentHistory: PaymentHistory[];
  transactionHistory: TransactionHistory[];
  riders: Rider[];
}

const PolicySelection = () => {
  const [customerName, setCustomerName] = useState('');
  const [policyNumber, setPolicyNumber] = useState('');
  const [customerId, setCustomerId] = useState('');
  const [searchClicked, setSearchClicked] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [currentCustomerData, setCurrentCustomerData] = useState<CurrentCustomerData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [policyDetails, setPolicyDetails] = useState<PolicyDetailsResponse | null>(null);
  const { setActiveTab, setSelectedCustomerData, setSelectedPolicyData } = useDashboard();

  // API Base URL - Replace with your actual backend URL
  const API_BASE_URL = (window as any).REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

  // Helper: localStorage key for caching recent searches
  const getStorageKey = (id: string, policy: string, name: string) => `customerData:${id.trim()}|${policy.trim()}|${name.trim()}`;

  // Save details to localStorage on successful search
  const saveToLocalStorage = (id: string, policy: string, name: string) => {
    const key = getStorageKey(id, policy, name);
    localStorage.setItem(key, JSON.stringify({ id, policy, name }));
  };

  // Try to auto-fill other fields if a match is found in localStorage
  const tryAutoFill = (changedField: 'id' | 'policy' | 'name', value: string) => {
    // Only auto-fill if the other fields are empty
    let found = false;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('customerData:')) {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        if (
          (changedField === 'id' && data.id === value && !policyNumber && !customerName) ||
          (changedField === 'policy' && data.policy === value && !customerId && !customerName) ||
          (changedField === 'name' && data.name === value && !customerId && !policyNumber)
        ) {
          if (changedField !== 'id') setCustomerId(data.id);
          if (changedField !== 'policy') setPolicyNumber(data.policy);
          if (changedField !== 'name') setCustomerName(data.name);
          found = true;
          break;
        }
      }
    }
    return found;
  };

  // 🔥 BACKEND ENDPOINT 1: SEARCH API
  // 📍 YOUR BACKEND NEEDS TO IMPLEMENT: POST /api/search
  const searchCustomerPolicies = async (customerId: string, policyNumber: string, customerName: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 🚀 BACKEND REQUEST: Send customer search data
      const response = await fetch(`${API_BASE_URL}/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId: customerId.trim(),
          policyNumber: policyNumber.trim(),
          customerName: customerName.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 📊 BACKEND RESPONSE: Customer data with available policies
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error searching customer policies:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 BACKEND ENDPOINT 2: VIEW API
  // 📍 YOUR BACKEND NEEDS TO IMPLEMENT: POST /api/view
  const getCompleteCustomerInfo = async (policyId: string, customerId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // 🚀 BACKEND REQUEST: Send policy and customer IDs
      const response = await fetch(`${API_BASE_URL}/view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          policyId: policyId,
          customerId: customerId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 📊 BACKEND RESPONSE: Complete customer information with payment history, transactions, riders
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching complete customer information:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // 🔥 BACKEND INTEGRATION POINT 1: SEARCH FUNCTIONALITY
  const handleSearch = async () => {
    if (!customerId.trim() || !policyNumber.trim() || !customerName.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    setSearchClicked(true);
    setError(null);

    try {
      // 🚀 BACKEND CALL: POST /api/search
      const customerData = await searchCustomerPolicies(customerId, policyNumber, customerName);

      if (customerData && customerData.success) {
        // ✅ SUCCESS: Display customer data and policies from backend
        setCurrentCustomerData({
          name: customerName,
          policyNumber: policyNumber,
          customerId: customerId,
          details: customerData.data // 📊 This comes from your backend
        });
        saveToLocalStorage(customerId, policyNumber, customerName);
      } else {
        // ❌ NO RESULTS: Show error message
        setCurrentCustomerData(null);
        setError(customerData?.message || 'Customer not found. Please check your details.');
      }
    } catch (error) {
      // ❌ ERROR: Network or server error
      console.error('Search error:', error);
      setCurrentCustomerData(null);
      setError('Failed to search customer data. Please try again.');
    }
  };

  // 🔥 BACKEND INTEGRATION POINT 2: VIEW POLICY DETAILS
  const handleSelectPolicy = async (policy: Policy) => {
    if (!currentCustomerData) return;

    try {
      setSelectedPolicy(policy);

      // 🚀 BACKEND CALL: POST /api/view
      const completeCustomerData = await getCompleteCustomerInfo(policy.id, currentCustomerData.customerId);

      if (completeCustomerData && completeCustomerData.success) {
        // ✅ SUCCESS: Display complete policy details from backend
        setPolicyDetails(completeCustomerData.data); // 📊 This comes from your backend
      } else {
        // ❌ ERROR: Failed to load policy details
        setError('Failed to load complete customer information');
      }
    } catch (error) {
      // ❌ ERROR: Network or server error
      console.error('Error selecting policy:', error);
      setError('Failed to load complete customer information. Please try again.');
    }
  };

  const renderSearchSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 className="text-xl font-bold text-black mb-6 pb-3 border-b border-gray-200">
        Search Your Policy
      </h2>
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer ID *</label>
          <input
            type="text"
            value={customerId}
            onChange={(e) => {
              const value = e.target.value;
              setCustomerId(value);
              setError(null); // Clear error when user types

              // Try to auto-fill from localStorage cache
              if (value) tryAutoFill('id', value);
            }}
            placeholder="Enter customer ID"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Policy Number *</label>
          <input
            type="text"
            value={policyNumber}
            onChange={(e) => {
              setPolicyNumber(e.target.value);
              setError(null); // Clear error when user types
              if (e.target.value) tryAutoFill('policy', e.target.value);
            }}
            placeholder="Enter policy number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer Name *</label>
          <input
            type="text"
            value={customerName}
            onChange={(e) => {
              setCustomerName(e.target.value);
              setError(null); // Clear error when user types
              if (e.target.value) tryAutoFill('name', e.target.value);
            }}
            placeholder="Enter customer name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>
        <div className="flex items-end">
          <button
            onClick={handleSearch}
            disabled={isLoading || !customerId.trim() || !policyNumber.trim() || !customerName.trim()}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <Search className="w-4 h-4" />
                Search
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  // 🔥 UI SECTION: DISPLAY POLICIES FROM BACKEND
  const renderAvailablePolicies = () => {
    if (!currentCustomerData) return null;
    // 📊 BACKEND DATA: Available policies from search API response
    const availablePolicies = currentCustomerData.details["Available Policies"] || [];
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-black">Your Policies</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sum Assured
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Annual Premium
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {availablePolicies.map((policy: Policy, index: number) => {
                const status = index < 2 ? "Active" : "Inactive";
                const statusClass = status === "Active"
                  ? "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
                  : "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800";
                // Format coverage (sum assured)
                let sumAssured = policy.coverage;
                if (sumAssured.match(/^[\d,.]+ ?\$/)) {
                  sumAssured = `$${sumAssured.replace(/ ?\$/g, "")}`;
                } else if (sumAssured.match(/^[\d,.]+$/)) {
                  sumAssured = `$${sumAssured}`;
                }
                // Format premium (annual premium)
                let annualPremium = policy.premium;
                if (annualPremium.match(/^[\d,.]+ ?\$/)) {
                  annualPremium = `$${annualPremium.replace(/ ?\$/g, "")}`;
                } else if (annualPremium.match(/^[\d,.]+$/)) {
                  annualPremium = `$${annualPremium}`;
                } else if (annualPremium.match(/^[\d,.]+ ?\$ annually/)) {
                  annualPremium = `$${annualPremium.replace(/ ?\$ annually/g, "")} / year`;
                }
                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {policy.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={statusClass}>{status}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 text-right">
                      {sumAssured}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {annualPremium.replace(/ annually| per year|\/year/gi, "")}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleSelectPolicy(policy)}
                        disabled={isLoading}
                        className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                      >
                        {isLoading && selectedPolicy?.id === policy.id ? (
                          <>
                            <Loader2 className="w-3 h-3 animate-spin inline mr-1" />
                            Loading...
                          </>
                        ) : (
                          'View Details'
                        )}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderSelectedPolicyDetails = () => {
    if (!selectedPolicy || !currentCustomerData) return null;

    // Use data from API response or fallback to empty arrays
    const paymentHistory = policyDetails?.paymentHistory || [];
    const transactionHistory = policyDetails?.transactionHistory || [];
    const riders = policyDetails?.riders || [];
    // Format premium to show only $ and amount
    const getPremiumAmount = (premium: string) => {
      let match = premium.match(/([\d,.]+) ?\$/);
      if (match) return `$${match[1]}`;
      match = premium.match(/\$([\d,.]+)/);
      if (match) return `$${match[1]}`;
      match = premium.match(/([\d,.]+)/);
      if (match) return `$${match[1]}`;
      return premium;
    };
    // Format coverage (face amount) to show $ first
    const getFaceAmount = (coverage: string) => {
      let match = coverage.match(/([\d,.]+) ?\$/);
      if (match) return `$${match[1]}`;
      match = coverage.match(/\$([\d,.]+)/);
      if (match) return `$${match[1]}`;
      match = coverage.match(/([\d,.]+)/);
      if (match) return `$${match[1]}`;
      return coverage;
    };
    return (
      <div className="space-y-8">
        <h2 className="text-xl font-semibold text-gray-900 pb-3 border-b border-gray-200">
          Selected Policy Details
        </h2>
        {/* Customer and Policy Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Customer Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Full Name</span>
                <span className="text-sm text-gray-900">{currentCustomerData.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Date of Birth</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.DOB}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Customer ID</span>
                <span className="text-sm text-gray-900">{currentCustomerData.customerId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Email</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.Email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Phone</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.Phone}</span>
              </div>
            </div>
          </div>
          {/* Coverage Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Coverage Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{getFaceAmount(selectedPolicy.coverage)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{getPremiumAmount(selectedPolicy.premium)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium Type</span>
                <span className="text-sm text-gray-900">Level Premium</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">Annually</span>
              </div>
            </div>
          </div>
          {/* Policy Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Policy Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Number</span>
                <span className="text-sm text-gray-900">{selectedPolicy.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Issue Date</span>
                <span className="text-sm text-gray-900">{selectedPolicy.issueDate || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  selectedPolicy.status === 'Active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedPolicy.status || 'Active'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{getPremiumAmount(selectedPolicy.premium)}</span>
              </div>
            </div>
          </div>
          {/* Financial Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Financial Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{getFaceAmount(selectedPolicy.coverage)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Cash Value</span>
                <span className="text-sm text-gray-900">{selectedPolicy.cashValue || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{getPremiumAmount(selectedPolicy.premium)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">{selectedPolicy.paymentFrequency || 'Annually'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Next Due Date</span>
                <span className="text-sm text-gray-900">{selectedPolicy.nextDueDate || 'N/A'}</span>
              </div>
            </div>
          </div>
        </div>
        {/* Payment History and Riders */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Payment History</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {paymentHistory.map((payment, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* Active Riders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Active Riders</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Rider</th>
                    <th className="px-6 py-3 text-right text-xs font-semibold text-gray-900 uppercase">Coverage</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {riders.length > 0 ? (
                    riders.map((rider, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">{rider.coverage}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rider.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {rider.status}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                        No active riders found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-black">Transaction History</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Transaction ID</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Remarks</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {transactionHistory.map((transaction, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.remarks}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  const renderNoResultsFound = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">
            {error || 'Customer not found. Please check your details and try again.'}
          </p>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800 font-medium mb-2">Please ensure:</p>
          <div className="text-blue-700 text-sm space-y-1">
            <p>• Customer ID is correct and active</p>
            <p>• Policy Number matches the customer record</p>
            <p>• Customer Name is spelled correctly</p>
            <p>• All fields are filled in completely</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIllustrationSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <h3 className="text-lg font-bold text-black mb-4">Ready to proceed?</h3>
      <p className="text-gray-600 mb-6">Generate detailed policy illustrations and projections</p>
      <button
        className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
        onClick={() => {
          if (selectedPolicy && currentCustomerData) {
            // Save selected customer and policy data to context
            setSelectedCustomerData({
              name: currentCustomerData.name,
              policyNumber: currentCustomerData.policyNumber,
              customerId: currentCustomerData.customerId,
              details: currentCustomerData.details
            });

            setSelectedPolicyData({
              id: selectedPolicy.id,
              name: selectedPolicy.name,
              description: selectedPolicy.description,
              coverage: selectedPolicy.coverage,
              premium: selectedPolicy.premium,
              features: selectedPolicy.features
            });

            setActiveTab('as-is');
          }
        }}
        disabled={!selectedPolicy || !currentCustomerData}
      >
        <Calculator className="w-5 h-5" />
        📊 Go to Illustration
      </button>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Search Section */}
      {renderSearchSection()}
      {/* Search Results */}
      {searchClicked && (
        <>
          {currentCustomerData ? (
            <>
              {renderAvailablePolicies()}
              {selectedPolicy && renderSelectedPolicyDetails()}
            </>
          ) : (
            renderNoResultsFound()
          )}
        </>
      )}
      {/* Info Message when no search performed */}
      {!searchClicked && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800">Enter customer details and click Search to view available policies.</p>
        </div>
      )}
      {/* Illustration Section */}
      {renderIllustrationSection()}
    </div>
  );
};

export default PolicySelection;