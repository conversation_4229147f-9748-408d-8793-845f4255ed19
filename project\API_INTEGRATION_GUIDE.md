# PolicySelection Component - Backend Integration Guide

## Overview
The PolicySelection component has been updated to integrate with backend APIs. All dummy data has been removed and replaced with API calls. This document outlines the required backend endpoints and data structures.

## Required API Endpoints

### 1. Customer Search Endpoint
**Endpoint:** `POST /api/customers/search`

**Purpose:** Search for customer information and their available policies based on customer details.

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "customerId": "string",
  "policyNumber": "string", 
  "customerName": "string"
}
```

**Response Format:**
```json
{
  "success": boolean,
  "data": {
    "DOB": "string",
    "Email": "string",
    "Phone": "string",
    "Address": "string",
    "Occupation": "string",
    "Annual Income": "string",
    "Customer ID": "string",
    "Policy Number": "string",
    "Policy Type": "string",
    "Status": "string",
    "Available Policies": [
      {
        "id": "string",
        "name": "string",
        "description": "string",
        "coverage": "string",
        "premium": "string",
        "features": ["string"],
        "status": "string",
        "issueDate": "string",
        "nextDueDate": "string",
        "cashValue": "string",
        "paymentFrequency": "string"
      }
    ]
  },
  "message": "string (optional)"
}
```

### 2. Policy Details Endpoint
**Endpoint:** `GET /api/policies/{policyId}/details`

**Purpose:** Get detailed policy information including payment history, transaction history, and active riders.

**Request Headers:**
```
Content-Type: application/json
Customer-Id: string
```

**Response Format:**
```json
{
  "success": boolean,
  "data": {
    "policy": {
      "id": "string",
      "name": "string", 
      "description": "string",
      "coverage": "string",
      "premium": "string",
      "features": ["string"],
      "status": "string",
      "issueDate": "string",
      "nextDueDate": "string",
      "cashValue": "string",
      "paymentFrequency": "string"
    },
    "paymentHistory": [
      {
        "date": "string",
        "amount": "string",
        "status": "string"
      }
    ],
    "transactionHistory": [
      {
        "id": "string",
        "date": "string",
        "type": "string",
        "amount": "string",
        "remarks": "string"
      }
    ],
    "riders": [
      {
        "name": "string",
        "coverage": "string",
        "status": "string"
      }
    ]
  },
  "message": "string (optional)"
}
```

## Environment Configuration

Set the following environment variable in your `.env` file:
```
REACT_APP_API_BASE_URL=http://your-backend-url/api
```

If not set, it defaults to `http://localhost:3001/api`

## Component Features

### Search Functionality
- Users enter Customer ID, Policy Number, and Customer Name
- All fields are required for search
- Loading states and error handling included
- Results cached in localStorage for auto-fill functionality

### Policy Display
- Shows all available policies for the customer in a table format
- Displays policy number, status, type, coverage amount, and premium
- "View Details" button for each policy

### Policy Details View
- Comprehensive policy information display
- Customer information section
- Coverage and financial details
- Payment history table
- Active riders information
- Transaction history

### Error Handling
- Network error handling
- User-friendly error messages
- Loading states for all API calls
- Form validation

## Implementation Notes

1. **Authentication:** Add authentication headers as needed for your backend
2. **Error Codes:** Handle specific HTTP status codes appropriately
3. **Data Validation:** Validate API responses before using the data
4. **Caching:** Consider implementing proper caching strategies
5. **Pagination:** Add pagination for large datasets if needed

## Testing

To test the component:
1. Ensure your backend implements the required endpoints
2. Set the correct API base URL
3. Test with valid customer data
4. Verify error handling with invalid data
5. Check loading states and user experience
