# Backend Integration Guide for PolicySelection Component

## Overview
This guide explains exactly how to integrate the PolicySelection component with your backend. All dummy data has been completely removed, and the component now relies on two simple API endpoints.

## 🔥 Required Backend Endpoints

### 1. Search API
**Endpoint:** `POST /api/search`

**Purpose:** Search for customer policies using any combination of customer ID, policy number, or name.

**Request:**
```json
{
  "customerId": "CUS-123456",
  "policyNumber": "POL-789012", 
  "customerName": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "DOB": "05.02.1994",
    "Email": "<EMAIL>",
    "Phone": "(*************",
    "Address": "123 Main Street, New York, NY 10001",
    "Occupation": "Software Engineer",
    "Annual Income": "$85,000",
    "Customer ID": "CUS-123456",
    "Policy Number": "POL-789012",
    "Policy Type": "Whole Life Insurance",
    "Status": "Active",
    "Available Policies": [
      {
        "id": "POL-789012",
        "name": "Whole Life Insurance",
        "description": "Permanent life insurance with cash value",
        "coverage": "500,000 $",
        "premium": "2000 $ annually",
        "features": ["Cash Value Growth", "Tax Benefits", "Loan Options"]
      },
      {
        "id": "POL-789013", 
        "name": "Term Life Insurance",
        "description": "Affordable temporary coverage",
        "coverage": "750,000 $",
        "premium": "800 $ annually",
        "features": ["Lower Premiums", "Convertible Option"]
      }
    ]
  }
}
```

### 2. View API
**Endpoint:** `POST /api/view`

**Purpose:** Get complete customer information for a selected policy.

**Request:**
```json
{
  "policyId": "POL-789012",
  "customerId": "CUS-123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "policy": {
      "id": "POL-789012",
      "name": "Whole Life Insurance",
      "description": "Permanent life insurance with cash value",
      "coverage": "500,000 $",
      "premium": "2000 $ annually",
      "features": ["Cash Value Growth", "Tax Benefits"],
      "status": "Active",
      "issueDate": "01/01/2024",
      "nextDueDate": "01/01/2025",
      "cashValue": "$25,000",
      "paymentFrequency": "Annually"
    },
    "paymentHistory": [
      {
        "date": "01/15/2024",
        "amount": "$2000",
        "status": "Paid"
      }
    ],
    "transactionHistory": [
      {
        "id": "TNX001",
        "date": "03-01-2024",
        "type": "Premium Payment",
        "amount": "2000.00",
        "remarks": "Annual premium payment"
      }
    ],
    "riders": [
      {
        "name": "Critical Illness Rider",
        "coverage": "$50,000",
        "status": "Active"
      }
    ]
  }
}
```

## 🚀 Integration Steps

1. **Create Backend Endpoints:**
   - Implement the two API endpoints described above
   - Ensure they return data in the exact format shown

2. **Set API Base URL:**
   - In your frontend environment, set:
   ```
   REACT_APP_API_BASE_URL=http://your-backend-url/api
   ```

3. **Test Integration:**
   - Start your backend server
   - Run the frontend application
   - Test the search functionality
   - Test the view details functionality

## 📋 Code Locations

The PolicySelection component has been updated with clear comments showing where backend integration happens:

### Search API Integration:
```typescript
// 🔥 BACKEND ENDPOINT 1: SEARCH API
// 📍 YOUR BACKEND NEEDS TO IMPLEMENT: POST /api/search
const searchCustomerPolicies = async (customerId, policyNumber, customerName) => {
  // Implementation details...
}

// 🔥 BACKEND INTEGRATION POINT 1: SEARCH FUNCTIONALITY
const handleSearch = async () => {
  // 🚀 BACKEND CALL: POST /api/search
  const customerData = await searchCustomerPolicies(customerId, policyNumber, customerName);
  
  // ✅ SUCCESS: Display customer data and policies from backend
  setCurrentCustomerData({
    name: customerName,
    policyNumber: policyNumber,
    customerId: customerId,
    details: customerData.data // 📊 This comes from your backend
  });
}
```

### View API Integration:
```typescript
// 🔥 BACKEND ENDPOINT 2: VIEW API  
// 📍 YOUR BACKEND NEEDS TO IMPLEMENT: POST /api/view
const getCompleteCustomerInfo = async (policyId, customerId) => {
  // Implementation details...
}

// 🔥 BACKEND INTEGRATION POINT 2: VIEW POLICY DETAILS
const handleSelectPolicy = async (policy) => {
  // 🚀 BACKEND CALL: POST /api/view
  const completeCustomerData = await getCompleteCustomerInfo(policy.id, currentCustomerData.customerId);
  
  // ✅ SUCCESS: Display complete policy details from backend
  setPolicyDetails(completeCustomerData.data); // 📊 This comes from your backend
}
```

## 🎯 Important Notes

1. **Error Handling:**
   - The component handles API errors gracefully
   - Backend should return appropriate HTTP status codes
   - Include `success` and `message` fields in responses

2. **Data Format:**
   - Follow the exact response format shown above
   - All fields are required unless marked optional

3. **Performance:**
   - Consider caching frequently accessed data
   - Optimize database queries for these endpoints

4. **Security:**
   - Implement proper authentication for these endpoints
   - Validate all input data on the server side

## 🔍 Testing

Test your backend implementation with these scenarios:

1. **Valid Search:** All fields match a customer
2. **Partial Search:** Only some fields match
3. **No Results:** No matching customer found
4. **Error Handling:** Server errors, timeouts
5. **View Details:** Complete policy information retrieval

The component is now completely ready for backend integration!
